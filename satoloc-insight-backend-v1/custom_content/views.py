from django.shortcuts import render
from django.conf import settings
from django.http import HttpResponse, JsonResponse
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from openai import OpenAI
import logging
import json
import re
from urllib.parse import quote
import requests
from datetime import date
from .models import CustomContent, ContentEnhancement
from .serializers import (
    CustomContentSerializer,
    ContentEnhancementSerializer,
)
from .utils.s3_utils import upload_ai_image_to_s3
from wp_content_sync.api_client import WordPressAPIClient, WordPressAPIClientError

logger = logging.getLogger(__name__)


class CustomContentViewSet(viewsets.ModelViewSet):
    serializer_class = CustomContentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return CustomContent.objects.filter(user=self.request.user).order_by(
            "-created_at"
        )

    def _clean_content_separators(self, content):
        """
        Clean unwanted horizontal rule separators from generated content.
        Removes standalone "---" lines and <p>---</p> HTML tags that OpenAI
        often generates as section separators.
        """
        if not content:
            return content

        # Remove standalone "---" lines in markdown
        content = re.sub(r"^---\s*$", "", content, flags=re.MULTILINE)

        # Remove <p>---</p> HTML tags
        content = re.sub(r"<p>\s*---\s*</p>", "", content, flags=re.IGNORECASE)

        # Remove multiple consecutive newlines that might be left after removing separators
        content = re.sub(r"\n\s*\n\s*\n", "\n\n", content)

        # Clean up any leading/trailing whitespace
        content = content.strip()

        return content

    def _process_content_for_wordpress(self, html_content):
        """
        Process HTML content from the visual editor for WordPress publishing.
        Ensures proper formatting and removes any problematic elements.
        """
        if not html_content:
            return html_content
        
        # Debug logging for color and text-decoration issues
        if 'color' in html_content.lower() or 'text-decoration' in html_content.lower():
            logger.info("WordPress processing - Input content: %s...", html_content[:500])
        
        # Clean up the HTML content
        processed_content = html_content
        
        # Remove any empty paragraphs that might cause display issues
        processed_content = re.sub(r'<p[^>]*>\s*</p>', '', processed_content, flags=re.IGNORECASE)
        
        # Helper function to convert RGB colors to hex for WordPress compatibility
        def rgb_to_hex(rgb_match):
            try:
                r = int(rgb_match.group(1))
                g = int(rgb_match.group(2))
                b = int(rgb_match.group(3))
                return f"#{r:02x}{g:02x}{b:02x}"
            except (ValueError, IndexError):
                return rgb_match.group(0)  # Return original if conversion fails
        
        # Remove specific problematic CSS properties while preserving user styling
        # Remove editor-specific properties that interfere with WordPress but keep user styling
        def clean_style_attribute(match):
            style_content = match.group(1)
            original_style = style_content  # For debugging
            
            # Remove problematic properties but keep user styling like color, font-size, etc.
            # Remove position, outline, and automatic margins added by the editor
            style_content = re.sub(r'\s*position\s*:[^;]*;?', '', style_content, flags=re.IGNORECASE)
            style_content = re.sub(r'\s*outline\s*:[^;]*;?', '', style_content, flags=re.IGNORECASE)
            style_content = re.sub(r'\s*outline-offset\s*:[^;]*;?', '', style_content, flags=re.IGNORECASE)
            # Only remove automatic margins, not user-set margins
            style_content = re.sub(r'\s*margin-top\s*:\s*(?:24px|12px|16px|20px)\s*;?', '', style_content, flags=re.IGNORECASE)
            style_content = re.sub(r'\s*margin-bottom\s*:\s*(?:24px|12px|16px|20px)\s*;?', '', style_content, flags=re.IGNORECASE)
            
            # Convert RGB colors to hex for WordPress compatibility
            style_content = re.sub(r'rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)', rgb_to_hex, style_content)
            
            # Convert text-decoration-line to text-decoration for WordPress compatibility
            style_content = re.sub(r'text-decoration-line\s*:', 'text-decoration:', style_content, flags=re.IGNORECASE)
            
            # Clean up any double semicolons or trailing/leading semicolons
            style_content = re.sub(r';+', ';', style_content)
            style_content = style_content.strip('; ')
            
            # Debug logging for color and text-decoration issues
            if 'color' in original_style.lower() or 'text-decoration' in original_style.lower():
                logger.info("Style processing - Original: '%s' -> Cleaned: '%s'", original_style, style_content)
            
            # Return the cleaned style attribute, or empty string if no styles remain
            if style_content:
                return f' style="{style_content}"'
            else:
                return ''
        
        # Apply the style cleaning function to all style attributes
        processed_content = re.sub(r'\s*style="([^"]*)"', clean_style_attribute, processed_content, flags=re.IGNORECASE)
        
        # Clean up excessive whitespace and normalize line breaks
        processed_content = re.sub(r'\s+', ' ', processed_content)
        processed_content = re.sub(r'>\s+<', '><', processed_content)
        
        # Ensure content starts with a proper block element
        if processed_content and not re.match(r'^\s*<(?:p|div|h[1-6]|ul|ol|blockquote)', processed_content, re.IGNORECASE):
            processed_content = f'<p>{processed_content}</p>'
        
        # Clean up any remaining unwanted separators from the content
        processed_content = self._clean_content_separators(processed_content)
        
        # Limit content size if it's extremely large (WordPress has limits)
        max_content_length = 65535  # MySQL TEXT field limit
        if len(processed_content) > max_content_length:
            logger.warning("Content too large (%s chars), truncating to %s chars", len(processed_content), max_content_length)
            processed_content = processed_content[:max_content_length]
            # Ensure we don't cut off in the middle of an HTML tag
            last_complete_tag = processed_content.rfind('>')
            if last_complete_tag > 0:
                processed_content = processed_content[:last_complete_tag + 1]
        
        # Debug logging for color and text-decoration issues
        if 'color' in html_content.lower() or 'text-decoration' in html_content.lower():
            logger.info("WordPress processing - Output content: %s...", processed_content[:500])
        
        return processed_content.strip()

    @action(detail=False, methods=["post"])
    def generate(self, request):
        prompt = request.data.get("prompt")
        title = request.data.get("title", "Untitled Content")
        language = request.data.get("language", "en")
        keywords = request.data.get("keywords", "")

        if not prompt:
            return Response(
                {"error": "Prompt is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Build system prompt parts
            format_instruction = "Format your response as Markdown."
            language_instruction = ""
            if language != "en":
                language_instruction = f"Please respond in {language} language only. The entire response should be in {language}."

            keywords_instruction = ""
            if keywords:
                keywords_instruction = f"Make sure to incorporate these keywords in your response: {keywords}."

            today_str = date.today().isoformat()
            system_base = (
                "You are a fact-checked content generation assistant. "
                f"Today's date is {today_str}. "
                "Double-check every numeric fact, quote or statistic and, if unsure, omit it. "
                f"{format_instruction} {language_instruction} {keywords_instruction}"
            )

            messages = [
                {"role": "system", "content": system_base},
                {"role": "user", "content": prompt},
            ]

            # Use OpenAI to generate content based on the prompt
            client = OpenAI(api_key=settings.OPENAI_API_KEY)

            max_tokens = 2048

            response = client.chat.completions.create(
                model="gpt-4.1",
                messages=messages,
                temperature=0.3,
                top_p=0.8,
                max_tokens=max_tokens,
            )

            generated_content = response.choices[0].message.content

            # Clean unwanted separators from generated content
            generated_content = self._clean_content_separators(generated_content)
            
            # Extract headline from generated content if available
            extracted_headline = self._extract_headline_from_content(generated_content)
            final_title = extracted_headline if extracted_headline else title

            # Create a new CustomContent object
            serializer = self.get_serializer(
                data={
                    "title": final_title,
                    "prompt": prompt,
                    "keywords": keywords,
                    "content": generated_content,
                    "language": language,
                }
            )

            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["post"])
    def optimize_headline(self, request):
        """Generate optimized headline suggestions for content"""
        content = request.data.get("content")
        original_headline = request.data.get("title")
        keywords = request.data.get("keywords")
        content_id = request.data.get("content_id")  # Get content ID

        print(f"Headers: {request.headers}")
        print(f"Request data keys: {request.data.keys()}")
        print(
            f"Headline enhancement - content_id: {content_id}, type: {type(content_id)}"
        )

        if not content:
            return Response(
                {"error": "Content is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            client = OpenAI(api_key=settings.OPENAI_API_KEY)

            response = client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {
                        "role": "system",
                        "content": """You are a headline optimization specialist. Generate 5 engaging, click-worthy headline alternatives that accurately represent the content. 
                    Format as a JSON array of strings. ONLY return the JSON array, no other text.
                    The headline should be in the same language as the original headline.
                    The headline should be no longer than 65 characters.
                    The headline should be no shorter than 30 characters.
                    The headline should be unique and not generic.
                    The headline should be relevant to the content.
                    """,
                    },
                    {
                        "role": "user",
                        "content": f"Original headline: {original_headline}\n\nKeywords: {keywords}\n\nContent: {content[:1000]}...",
                    },  # Send first 1000 chars to save tokens
                ],
                temperature=0.7,
                response_format={"type": "json_object"},
            )

            headline_suggestions = json.loads(response.choices[0].message.content)

            # Save the enhancement if content_id is provided
            if content_id:
                try:
                    print(f"Looking up content with ID: {content_id}")
                    # Ensure content_id is treated as integer
                    if isinstance(content_id, str) and content_id.isdigit():
                        content_id = int(content_id)
                    elif not isinstance(content_id, int):
                        print(f"Invalid content_id format: {content_id}")
                        raise ValueError(f"Invalid content_id format: {content_id}")

                    custom_content = CustomContent.objects.get(
                        id=content_id, user=request.user
                    )
                    print(f"Found content: {custom_content.title}")

                    # Create enhancement record
                    enhancement = ContentEnhancement.objects.create(
                        custom_content=custom_content,
                        enhancement_type="headline",
                        data=headline_suggestions,
                    )
                    print(f"Enhancement created with ID: {enhancement.id}")
                except CustomContent.DoesNotExist:
                    print(
                        f"Content with ID {content_id} not found for user {request.user}"
                    )
                    pass  # Silently continue if content doesn't exist or doesn't belong to user
                except Exception as e:
                    print(f"Error creating enhancement: {str(e)}")
            else:
                print("No content_id provided, skipping enhancement creation")

            return Response(headline_suggestions, status=status.HTTP_200_OK)

        except Exception as e:
            print(f"Error in headline optimization: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["post"])
    def suggest_images(self, request):
        """Suggest image ideas for the content"""
        content = request.data.get("content")
        content_id = request.data.get("content_id")  # Get content ID

        if not content:
            return Response(
                {"error": "Content is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            client = OpenAI(api_key=settings.OPENAI_API_KEY)

            response = client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {
                        "role": "system",
                        "content": """You are a visual content strategist. 
                     Suggest 3-5 image ideas that would complement the content well. 
                     For each image, provide a title, description, and relevant search terms that could be used to find such an image. 
                     Format the response as a JSON object with a 'suggestions' array containing objects with 'title', 'description', and 'search_terms' (array) properties. 
                     Format example: {'suggestions': [{'title': '...', 'description': '...', 'search_terms': ['...', '...']}]}""",
                    },
                    {
                        "role": "user",
                        "content": f"Content: {content[:1500]}...",
                    },  # Send first 1500 chars to save tokens
                ],
                temperature=0.7,
                response_format={"type": "json_object"},
            )

            image_suggestions = json.loads(response.choices[0].message.content)

            # Verify the response structure
            if "suggestions" not in image_suggestions:
                # If OpenAI returned an array directly instead of a 'suggestions' array property
                if isinstance(image_suggestions, list):
                    # Convert the direct array to the expected format
                    image_suggestions = {"suggestions": image_suggestions}
                else:
                    # Create a default structure if the response was invalid
                    image_suggestions = {"suggestions": []}

            # Enhance with image search URLs (Unsplash, Pexels, etc.)
            for suggestion in image_suggestions.get("suggestions", []):
                if "search_terms" in suggestion:
                    search_query = " ".join(
                        suggestion["search_terms"][:3]
                    )  # Use first 3 terms
                    suggestion["unsplash_url"] = (
                        f"https://unsplash.com/s/photos/{quote(search_query)}"
                    )
                    suggestion["pexels_url"] = (
                        f"https://www.pexels.com/search/{quote(search_query)}/"
                    )

            # Save the enhancement if content_id is provided
            if content_id:
                try:
                    custom_content = CustomContent.objects.get(
                        id=content_id, user=request.user
                    )

                    # Create enhancement record
                    ContentEnhancement.objects.create(
                        custom_content=custom_content,
                        enhancement_type="image",
                        data=image_suggestions,
                    )
                except CustomContent.DoesNotExist:
                    pass  # Silently continue if content doesn't exist or doesn't belong to user

            print("Returning image suggestions:", image_suggestions)
            return Response(image_suggestions, status=status.HTTP_200_OK)

        except Exception as e:
            print("Error generating image suggestions:", str(e))
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["post"])
    def generate_ai_image(self, request):
        """Generate an image using AI (GPT Image) based on the content"""
        content = request.data.get("content")
        prompt = request.data.get("prompt", "")
        size = request.data.get("size", "1024x1024")  # Default size
        model = "gpt-image-1"  # Only use GPT Image model
        quality = request.data.get("quality", "medium")  # Quality parameter
        style = request.data.get(
            "style", "standard"
        )  # Background parameter for GPT Image
        content_id = request.data.get("content_id")  # Get content ID

        # Validate parameters
        if not content and not prompt:
            return Response(
                {"error": "Either content or prompt is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate size
        valid_sizes = ["1024x1024", "1536x1024", "1024x1536", "auto"]

        if size not in valid_sizes:
            return Response(
                {
                    "error": f"Invalid size for GPT Image. Valid sizes are {', '.join(valid_sizes)}"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate quality
        if quality not in ["low", "medium", "high", "auto"]:
            return Response(
                {
                    "error": "Quality must be one of 'low', 'medium', 'high', or 'auto' for GPT Image"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            client = OpenAI(api_key=settings.OPENAI_API_KEY)

            # If no specific prompt is provided, generate one based on the content
            if not prompt and content:
                prompt_response = client.chat.completions.create(
                    model="gpt-4.1",
                    messages=[
                        {
                            "role": "system",
                            "content": """You are an AI image prompt expert. 
                         Create a detailed, descriptive prompt for image generation that represents the given content. 
                         Focus on visual elements, style, and mood. Keep the prompt under 1000 characters.""",
                        },
                        {"role": "user", "content": f"Content: {content[:1500]}..."},
                    ],
                    temperature=0.7,
                    max_tokens=500,
                )
                prompt = prompt_response.choices[0].message.content

            # Ensure prompt is not too long
            if len(prompt) > 1000:
                prompt = prompt[:1000]

            # Prepare parameters for GPT Image - URLs are returned by default
            image_params = {
                "model": model,
                "prompt": prompt,
                "n": 1,
            }

            # Add optional parameters that are documented and supported
            if size != "auto":
                image_params["size"] = size

            if quality != "auto":
                image_params["quality"] = quality

            # Generate the image
            try:
                image_response = client.images.generate(**image_params)
            except Exception as e:
                logger.error(f"Error during OpenAI API call: {str(e)}")
                raise

            # Prepare response
            image_data = {
                "prompt": prompt,
                "model": model,
                "size": size,
            }

            # For debugging
            print(f"GPT Image response: {image_response.data[0]}")

            # Get the base64 image data from the response and upload to S3
            if hasattr(image_response.data[0], "b64_json") and image_response.data[0].b64_json:
                try:
                    # Upload base64 image to S3 and get public URL
                    s3_url = upload_ai_image_to_s3(image_response.data[0].b64_json)
                    image_data["url"] = s3_url
                    print(f"AI Image uploaded to S3 successfully: {s3_url}")
                    logger.info(f"AI image generated and uploaded to S3 for content ID {content_id}: {s3_url}")
                except Exception as s3_error:
                    logger.error(f"Failed to upload AI image to S3: {str(s3_error)}")
                    raise Exception(f"Failed to upload image to S3: {str(s3_error)}")
            else:
                print(f"AI Image: No base64 data found in response")
                logger.error(f"AI image generation failed - no image data returned")
                raise Exception("Failed to generate image - no image data returned from API")

            image_data["quality"] = quality
            image_data["style"] = style  # Using style field for background

            # Save the enhancement if content_id is provided
            if content_id:
                try:
                    custom_content = CustomContent.objects.get(
                        id=content_id, user=request.user
                    )

                    # Create enhancement record
                    ContentEnhancement.objects.create(
                        custom_content=custom_content,
                        enhancement_type="ai-image",
                        data=image_data,
                    )
                except CustomContent.DoesNotExist:
                    pass  # Silently continue if content doesn't exist or doesn't belong to user

            return Response(image_data, status=status.HTTP_200_OK)

        except Exception as e:
            print(f"Error generating image with GPT Image:", str(e))
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["post"])
    def find_citations(self, request):
        """Find potential citations and sources for the content"""
        content = request.data.get("content")
        content_id = request.data.get("content_id")  # Get content ID

        if not content:
            return Response(
                {"error": "Content is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            client = OpenAI(api_key=settings.OPENAI_API_KEY)

            # Extract main topics and claims from content
            analysis_response = client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a research assistant. Extract the main topics, facts, statistics, and claims from this content that would benefit from citations. Format as a JSON array of strings. Each item should be a specific statement that needs a source. ONLY return the JSON array.",
                    },
                    {"role": "user", "content": f"Content: {content}"},
                ],
                temperature=0.3,
                response_format={"type": "json_object"},
            )

            claims = json.loads(analysis_response.choices[0].message.content)

            # For each claim, suggest potential sources
            sources_response = client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a scholarly research assistant. For each claim or topic, suggest 1-2 potential credible sources that could be used to back it up. Include title, author (if applicable), publication/website, year (if known), and a brief note about the source's relevance. Format as a JSON object with claim texts as keys and arrays of source objects as values. ONLY return the JSON object.",
                    },
                    {
                        "role": "user",
                        "content": f"Claims needing citations: {json.dumps(claims)}",
                    },
                ],
                temperature=0.3,
                response_format={"type": "json_object"},
            )

            citation_suggestions = json.loads(
                sources_response.choices[0].message.content
            )

            # Save the enhancement if content_id is provided
            if content_id:
                try:
                    custom_content = CustomContent.objects.get(
                        id=content_id, user=request.user
                    )

                    # Create enhancement record
                    ContentEnhancement.objects.create(
                        custom_content=custom_content,
                        enhancement_type="citation",
                        data=citation_suggestions,
                    )
                except CustomContent.DoesNotExist:
                    pass  # Silently continue if content doesn't exist or doesn't belong to user

            return Response(citation_suggestions, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["post"])
    def check_plagiarism(self, request):
        """Check content for potential plagiarism and uniqueness"""
        content = request.data.get("content")
        content_id = request.data.get("content_id")  # Get content ID

        if not content:
            return Response(
                {"error": "Content is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            client = OpenAI(api_key=settings.OPENAI_API_KEY)

            # Split content into paragraphs
            paragraphs = re.split(r"\n\s*\n", content)

            # For demo purposes, we'll analyze the content with AI
            # In a production system, you would integrate with real plagiarism detection services
            response = client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a plagiarism detection tool. Analyze this content and identify any sections that seem generic, commonplace, or potentially copied from common sources. For each potential issue, provide the text segment and a brief explanation of why it might not be original. Also rate the overall uniqueness of the content on a scale of 1-10. Format response as a JSON object with 'uniqueness_score', 'potential_issues' (array of objects with 'text' and 'reason' properties), and 'improvement_suggestions' properties. ONLY return the JSON object.",
                    },
                    {"role": "user", "content": f"Content to analyze: {content}"},
                ],
                temperature=0.3,
                response_format={"type": "json_object"},
            )

            plagiarism_analysis = json.loads(response.choices[0].message.content)

            # Save the enhancement if content_id is provided
            if content_id:
                try:
                    custom_content = CustomContent.objects.get(
                        id=content_id, user=request.user
                    )

                    # Create enhancement record
                    ContentEnhancement.objects.create(
                        custom_content=custom_content,
                        enhancement_type="plagiarism",
                        data=plagiarism_analysis,
                    )
                except CustomContent.DoesNotExist:
                    pass  # Silently continue if content doesn't exist or doesn't belong to user

            return Response(plagiarism_analysis, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def proxy_image(self, request):
        """Proxy for fetching images to avoid CORS issues"""
        image_url = request.query_params.get("imageUrl")

        if not image_url:
            return Response(
                {"error": "Image URL is required in the 'imageUrl' parameter"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            print(f"Proxying image from: {image_url}")

            # Fetch the image from the provided URL
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
            }
            image_response = requests.get(
                image_url, stream=True, headers=headers, timeout=10
            )

            print(f"Proxy response status: {image_response.status_code}")
            if image_response.status_code != 200:
                print(f"Proxy error: {image_response.text[:200]}")
                return Response(
                    {"error": f"Failed to fetch image: {image_response.status_code}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get the content type from the response
            content_type = image_response.headers.get("Content-Type", "image/png")

            # Create a Django response with the image data
            response = HttpResponse(
                content=image_response.content, content_type=content_type
            )

            # Add download headers
            response["Content-Disposition"] = 'attachment; filename="dalle-image.png"'

            return response

        except Exception as e:
            print("Error proxying image:", str(e))
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["get"])
    def enhancements(self, request, pk=None):
        """Get all enhancements for a specific content"""
        try:
            custom_content = self.get_object()
            enhancements = ContentEnhancement.objects.filter(
                custom_content=custom_content
            )
            serializer = ContentEnhancementSerializer(enhancements, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["post"])
    def improve_prompt(self, request):
        """Improve and optimize a user's prompt using AI"""
        try:
            prompt = request.data.get("prompt", "")
            title = request.data.get("title", "")
            keywords = request.data.get("keywords", "")
            tone = request.data.get("tone", "professional")

            if not prompt:
                return Response(
                    {"error": "Prompt is required"}, status=status.HTTP_400_BAD_REQUEST
                )

            # Create system prompt for improving the user's prompt
            system_prompt = """
You are an expert prompt engineer. Your task is to improve and optimize user prompts to make them more effective for AI content generation.

Guidelines for improving prompts:
1. Make them more specific and detailed
2. Add context and background information
3. Include clear instructions for structure and format
4. Specify the target audience if not mentioned
5. Add relevant constraints or requirements
6. Ensure clarity and remove ambiguity
7. Maintain the original intent while enhancing effectiveness

Return only the improved prompt without any explanations or additional text.
"""

            # Build context for the improvement
            context_parts = []
            if title:
                context_parts.append(f"Title/Topic: {title}")
            if keywords:
                context_parts.append(f"Keywords: {keywords}")
            if tone:
                context_parts.append(f"Desired tone: {tone}")

            context = "\n".join(context_parts) if context_parts else ""

            user_prompt = f"""
Original prompt: {prompt}

{context}

Please improve this prompt to make it more effective for AI content generation.
"""

            # Call OpenAI API
            client = OpenAI(api_key=settings.OPENAI_API_KEY)

            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                max_tokens=500,
                temperature=0.7,
            )

            improved_prompt = response.choices[0].message.content.strip()

            # Clean any unwanted separators from the improved prompt
            improved_prompt = self._clean_content_separators(improved_prompt)

            return Response(
                {"improved_prompt": improved_prompt, "original_prompt": prompt},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"Error improving prompt: {str(e)}")
            return Response(
                {"error": f"Failed to improve prompt: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=['post'])
    def improve_content(self, request):
        """Improve and enhance content for specific elements using AI"""
        content = request.data.get('content')
        element_type = request.data.get('element_type', 'text')
        
        if not content or not content.strip():
            return Response(
                {"error": "Content is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        try:
            client = OpenAI(api_key=settings.OPENAI_API_KEY)
            
            # Create element-specific improvement prompt
            system_prompt = f"""
You are a professional content editor and writer. Your task is to improve and enhance {element_type} content to make it more engaging, clear, and well-written.

Guidelines for improving content:
1. Maintain the original meaning and core message
2. Enhance clarity and readability
3. Improve word choice and sentence structure
4. Make it more engaging and compelling
5. Ensure appropriate tone for the content type
6. Fix any grammar or style issues
7. Keep the same general length unless improvement requires expansion

Return ONLY the improved content without any explanations, quotes, or additional text.
"""
            
            user_prompt = f"Improve this {element_type} content:\n\n{content.strip()}"
            
            response = client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=1000,
                temperature=0.7,
            )
            
            improved_content = response.choices[0].message.content.strip()
            
            # Clean any unwanted separators or formatting
            improved_content = self._clean_content_separators(improved_content)
            
            # Remove quotes if the AI wrapped the content in quotes
            if ((improved_content.startswith('"') and improved_content.endswith('"')) or
                (improved_content.startswith("'") and improved_content.endswith("'"))):
                improved_content = improved_content[1:-1]
            
            return Response(
                {"improved_content": improved_content, "original_content": content},
                status=status.HTTP_200_OK,
            )
            
        except Exception as e:
            logger.error(f"Error improving content: {str(e)}")
            return Response(
                {"error": f"Failed to improve content: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=['post'])
    def push_to_wordpress(self, request, pk=None):
        """Push custom content to WordPress as a new post"""
        try:
            # Get the custom content object
            content_obj = self.get_object()
            
            # Get WordPress connection details from request
            wp_base_url = request.data.get('wp_base_url')
            wp_api_key = request.data.get('wp_api_key')
            post_status = request.data.get('status', 'draft')  # Default to draft
            categories = request.data.get('categories', [])  # Optional categories
            
            if not wp_base_url or not wp_api_key:
                return Response(
                    {"error": "WordPress base URL and API key are required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            
            # Initialize WordPress API client
            wp_client = WordPressAPIClient(wp_base_url, wp_api_key)
            
            # Process HTML content for WordPress
            processed_content = self._process_content_for_wordpress(content_obj.content)
            logger.info(f"Processing HTML content for WordPress: content ID {content_obj.id}, length: {len(processed_content)}")
            
            # Prepare data for WordPress
            wp_data = {
                'title': content_obj.title,
                'content': processed_content,
                'post_type': 'post',  # Create as a post
                'status': post_status,
            }
            
            # Add categories if provided
            if categories:
                wp_data['categories'] = categories
        
            # Check if this content already has a WordPress post ID
            is_update = bool(content_obj.wordpress_post_id)
        
            if is_update:
                # Update existing WordPress post
                logger.info(f"Updating existing WordPress post ID {content_obj.wordpress_post_id} for content ID {content_obj.id}")
                try:
                    wp_response = wp_client.update_content_item(content_obj.wordpress_post_id, wp_data)
                    message = "Content successfully updated in WordPress"
                    status_code = status.HTTP_200_OK
                except WordPressAPIClientError as e:
                    # Check if the error is due to invalid post ID (post was deleted from WordPress)
                    if "rest_post_invalid_id" in str(e) or "404" in str(e):
                        logger.warning(f"WordPress post ID {content_obj.wordpress_post_id} no longer exists. Creating new post instead.")
                        # Clear the invalid WordPress post ID
                        content_obj.wordpress_post_id = None
                        content_obj.save(update_fields=['wordpress_post_id'])
                        
                        # Create new WordPress post instead
                        wp_response = wp_client.create_content_item(wp_data)
                        
                        # Save the new WordPress post ID
                        content_obj.wordpress_post_id = wp_response.get('id')
                        content_obj.save(update_fields=['wordpress_post_id'])
                        
                        message = "WordPress post was recreated (original post no longer existed)"
                        status_code = status.HTTP_201_CREATED
                    else:
                        # Re-raise other WordPress API errors
                        raise
            else:
                # Create new WordPress post
                logger.info(f"Creating new WordPress post for content ID {content_obj.id}")
                wp_response = wp_client.create_content_item(wp_data)
                
                # Save the WordPress post ID to our model
                content_obj.wordpress_post_id = wp_response.get('id')
                content_obj.save(update_fields=['wordpress_post_id'])
                
                message = "Content successfully pushed to WordPress"
                status_code = status.HTTP_201_CREATED
        
            # Return success response with WordPress post details
            return Response({
                "message": message,
                "wordpress_post_id": wp_response.get('id'),
                "wordpress_url": wp_response.get('link'),
                "status": wp_response.get('status'),
                "custom_content_id": content_obj.id,
                "action": "updated" if is_update else "created"
            }, status=status_code)
            
        except WordPressAPIClientError as e:
            logger.error(f"WordPress API error pushing content ID {pk}: {str(e)}")
            return Response(
                {"error": f"WordPress API error: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            logger.error(f"Error pushing content ID {pk} to WordPress: {str(e)}")
            return Response(
                {"error": f"Failed to push content to WordPress: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    
    def _extract_headline_from_content(self, content):
        """
        Extract the first headline (H1 or H2) from markdown content.
        Returns the headline text without markdown symbols, or None if not found.
        """
        import re
        
        if not content:
            return None
            
        # Try to find first H1 heading (# Title)
        h1_match = re.search(r'^#\s+(.+)$', content, re.MULTILINE)
        if h1_match:
            return h1_match.group(1).strip()
        
        # Try to find first H2 heading (## Title)
        h2_match = re.search(r'^##\s+(.+)$', content, re.MULTILINE)
        if h2_match:
            return h2_match.group(1).strip()
        
        # Try to find bold text at the beginning (might be a title)
        bold_match = re.search(r'^\*\*(.+?)\*\*', content, re.MULTILINE)
        if bold_match:
            return bold_match.group(1).strip()
            
        return None
